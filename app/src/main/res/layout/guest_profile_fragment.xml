<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.guest.GuestProfileViewModel" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/mainBcg"
        tools:context=".ui.guest.GuestProfileFragment">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scrollView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainBcg"
            android:nestedScrollingEnabled="false"
            app:layout_constraintBottom_toTopOf="@+id/bottomButtons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:context=".ui.guest.GuestProfileFragment">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <FrameLayout
                    android:id="@+id/ltv_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <include
                        android:id="@+id/guest_ltv_layout"
                        layout="@layout/guest_ltv_layout"
                        android:layout_width="match_parent"
                        android:background="@color/white"
                        android:layout_height="wrap_content"
                        bind:loyaltyActive="@{viewmodel.loyaltyActive}"
                        bind:guest="@{viewmodel.guest}"
                        bind:posActive="@{viewmodel.posActive}"
                        bind:createMode="@{viewmodel.createMode}"
                        bind:currency="@{viewmodel.currency}" />

                </FrameLayout>

                <include
                    android:id="@+id/guest_data_layout"
                    layout="@layout/guest_data_layout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="32dp"
                    app:layout_constraintBottom_toTopOf="@+id/deleteBtn"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ltv_layout"
                    app:layout_constraintVertical_bias="0.0"
                    bind:guest="@{viewmodel.guest}"
                    bind:isFreemium="@{viewmodel.isFreemium}"
                    bind:isMarketingVisible="@{viewmodel.isMarketingVisible}"
                    bind:onPhoneGuest="@{() -> viewmodel.onPhoneGuest()}"
                    bind:permission="@{viewmodel.permissionGuest}"
                    bind:tagsAvailable="@{viewmodel.tagsAvailable}" />

                <com.eatapp.clementine.views.LoadingButton
                    android:id="@+id/deleteBtn"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@{!viewmodel.permissionGuest.boolValue || viewmodel.isFreemium ? @drawable/shape_rounded_btn_bcg_grey_outline_200 : @drawable/shape_rounded_btn_bcg_red_outline}"
                    android:clickable="@{viewmodel.permissionGuest.boolValue}"
                    android:enabled="@{!viewmodel.isFreemium}"
                    android:onClick="@{() -> viewmodel.onDeleteGuest()}"
                    android:visibility="@{viewmodel.createMode || viewmodel.simplifiedMode ? View.GONE : View.VISIBLE, default=visible}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:progressBarColor="@color/colorRed"
                    app:title="@string/delete_user"
                    app:tintColor="@{!viewmodel.permissionGuest.boolValue || viewmodel.isFreemium ? R.color.colorGrey200 : R.color.colorRed}" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/updateBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:background="@{!viewmodel.permissionGuest.boolValue || viewmodel.isFreemium ? @drawable/shape_rounded_btn_bcg_grey200 : @drawable/shape_rounded_btn_bcg_green}"
                android:clickable="@{viewmodel.permissionGuest.boolValue}"
                android:enabled="@{!viewmodel.isFreemium}"
                android:onClick="@{() -> viewmodel.onUpdateGuest()}"
                android:visibility="@{viewmodel.createMode ? View.GONE : View.VISIBLE, default=visible}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/white"
                app:title="@string/update_user"
                app:tintColor="@{!viewmodel.permissionGuest.boolValue || viewmodel.isFreemium ? R.color.colorGrey200 : R.color.white}" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/createBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                android:onClick="@{() -> viewmodel.onCreateGuest()}"
                android:clickable="@{viewmodel.permissionGuest.boolValue}"
                android:visibility="@{viewmodel.createMode ? View.VISIBLE : View.GONE, default=gone}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tintColor="@{R.color.white}"
                bind:layout_constraintEnd_toEndOf="parent"
                bind:layout_constraintStart_toStartOf="parent"
                bind:progressBarColor="@color/white"
                bind:title="@string/create_user" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>