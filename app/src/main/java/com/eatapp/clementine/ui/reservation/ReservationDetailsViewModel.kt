package com.eatapp.clementine.ui.reservation

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.BuildConfig
import com.eatapp.clementine.ConsumableEvent
import com.eatapp.clementine.ReservationVouchersHandler
import com.eatapp.clementine.VoucherUpdateState
import com.eatapp.clementine.VouchersHandler
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.response.apiresources.Country
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.data.network.response.comment.CommentModel
import com.eatapp.clementine.data.network.response.custom_fields.CustomField
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldAttributes
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldComponent
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.payment.PaymentStatus
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.restaurant.AccountStateType
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.shift.Shift
import com.eatapp.clementine.data.network.response.tag.Tag
import com.eatapp.clementine.data.network.response.tagging.TaggingAttributes
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.network.response.user.PermissionName
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignmentModel
import com.eatapp.clementine.data.network.response.vouchers.VoucherType
import com.eatapp.clementine.data.repository.ClosingsRepository
import com.eatapp.clementine.data.repository.CustomFieldsRepository
import com.eatapp.clementine.data.repository.GuestsRepository
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.data.repository.RestaurantUsersRepository
import com.eatapp.clementine.data.repository.ShiftsRepository
import com.eatapp.clementine.data.repository.TablesRepository
import com.eatapp.clementine.data.repository.TagRepository
import com.eatapp.clementine.data.repository.VoucherRepository
import com.eatapp.clementine.internal.BookingSource
import com.eatapp.clementine.internal.EatException
import com.eatapp.clementine.internal.ItemsGroup
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.SingleLiveEvent
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.ceilToQuarter
import com.eatapp.clementine.internal.customFieldType
import com.eatapp.clementine.internal.durationFormatted
import com.eatapp.clementine.internal.labelized
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.ConflictsManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.FeatureFlagsManager
import com.eatapp.clementine.internal.managers.ShiftManager
import com.eatapp.clementine.internal.managers.TagType
import com.eatapp.clementine.internal.managers.manageGuests
import com.eatapp.clementine.ui.base.BaseViewModel
import com.eatapp.clementine.ui.common.tables.conflict.ConflictItem
import com.eatapp.clementine.ui.reservation.ReservationDetailsItemType.Companion.defaultSorting
import com.eatapp.clementine.views.StepperView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.google.i18n.phonenumbers.PhoneNumberUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import javax.inject.Inject
import kotlin.math.abs

data class ReservationDetailsItem(
    val itemType: Int,
    var expanded: Boolean = false,
    val customField: CustomField? = null
) {
    override fun equals(other: Any?): Boolean {
        return other is ReservationDetailsItem
                && itemType == other.itemType
                && expanded == other.expanded
    }

    override fun hashCode(): Int {
        var result = itemType
        result = 31 * result + expanded.hashCode()
        result = 31 * result + (customField?.hashCode() ?: 0)
        return result
    }
}

enum class ReservationDetailsItemType(val itemType: Int, private var expanded: Boolean) {
    GUEST(0, false),
    GUEST_PROFILE(18, false),
    PREFERENCE(1, false),
    STATUS(2, false),
    SEND_MESSAGE(3, false),
    WALK_IN_WAITLIST(4, false),
    DATE_TIME(5, false),
    COVERS_DURATION(8, false),
    TABLE(9, false),
    WAIT_TIME(10, false),
    NOTES(11, false),
    COMMENTS(12, false),
    TAKER(13, false),
    TAGS(14, false),
    SOURCE(15, false),
    VOUCHER(16, false),
    LOYALTY(17, false),
    DELETE_RESERVATION(99, false),
    DIVIDER(100, false);

    companion object {
        val defaultSorting = arrayListOf(
            GUEST,
            GUEST_PROFILE,
            VOUCHER,
            DATE_TIME,
            COVERS_DURATION,
            STATUS,
            WAIT_TIME,
            SEND_MESSAGE,
            PREFERENCE,
            WALK_IN_WAITLIST,
            DIVIDER,
            TABLE,
            NOTES,
            LOYALTY,
            SOURCE,
            TAKER,
            TAGS,
            COMMENTS,
            DIVIDER
        )
    }

    fun toReservationListItem(): ReservationDetailsItem {
        return ReservationDetailsItem(itemType, expanded)
    }
}

enum class CounterListType {
    COVERS,
    CUSTOM_FIELD
}

@HiltViewModel
class ReservationDetailsViewModel @Inject constructor(
    val eatManager: EatManager,
    private val shiftManager: ShiftManager,
    val featureFlagsManager: FeatureFlagsManager,
    val analyticsManager: AnalyticsManager,
    private val closingsRepository: ClosingsRepository,
    private val reservationsRepository: ReservationsRepository,
    private val guestsRepository: GuestsRepository,
    private val sharedPreferences: SharedPreferences,
    private val conflictsManager: ConflictsManager,
    @ReservationVouchersHandler
    private val vouchersHandler: VouchersHandler,
    private val usersRepository: RestaurantUsersRepository,
    private val tablesRepository: TablesRepository,
    private val tagRepository: TagRepository,
    private val shiftRepository: ShiftsRepository,
    private val voucherRepository: VoucherRepository,
    private val customFieldsRepository: CustomFieldsRepository
) : BaseViewModel() {

    companion object {
        const val MAX_COVERS = 500
        const val RESERVATION_DETAILS_LIST_SORTING = "reservation_details_list_sorting_"
    }

    var restaurantId: String? = null

    var createMode = MutableLiveData<Boolean>()

    var update = MutableLiveData<Boolean>()
    var create = MutableLiveData<Boolean>()
    var delete = MutableLiveData<Boolean>()
    var tableReadyMessageSent = MutableLiveData<Boolean>()

    val permissionReservation: Permission =
        eatManager.permission(
            restaurantId ?: eatManager.restaurantId(),
            PermissionName.DISABLE_RESERVATION_DELETION.permissionName
        )

    val reservationEditorPermission: Permission =
        eatManager.permission(
            restaurantId ?: eatManager.restaurantId(),
            PermissionName.REQUIRE_RESERVATION_EDITED_BY.permissionName
        )

    val permissionReservationTaker: Permission =
        eatManager.permission(
            restaurantId ?: eatManager.restaurantId(),
            PermissionName.REQUIRE_RESERVATION_TAKEN_BY.permissionName
        )

    val manageReservationPermission: Permission =
        eatManager.permission(
            restaurantId ?: eatManager.restaurantId(),
            PermissionName.MANAGE_RESERVATIONS.permissionName
        )

    var permissionGuest: Permission = eatManager.permission(identifier = manageGuests)

    private var taggings: MutableList<Tagging>? = mutableListOf()

    var closings: MutableList<Closing> = mutableListOf()
    var reservations: MutableList<Reservation> = mutableListOf()
    val tables = tablesRepository.tables
    val takers = usersRepository.takers
    val vouchers = voucherRepository.vouchers
    val tags = tagRepository.reservationTags()

    val tagsAvailable: Boolean
        get() {
            return tags.isNotEmpty()
        }

    var hideReservationTaker = takers.value?.isEmpty() == true

    val isFreemium = eatManager.accountState() == AccountStateType.IN_HOUSE
    val isMarketingVisible = eatManager.restaurant()?.marketingOptInVisibility ?: false
    var currency: String = eatManager.restaurant()?.currency ?: "AED"
    val posActive: Boolean = eatManager.restaurant()?.posActive == true
    val loyaltyActive: Boolean = eatManager.restaurant()?.loyaltyEnabled == true

    val isReservationUpdatable = eatManager.accountState() == AccountStateType.ACTIVE ||
            !featureFlagsManager.badgerEnabled

    var initialStatus: String = ""
    private var initialReservationState: ReservationState? = null

    var userChangedReservationStatus = false

    private val _reservation = MutableLiveData<Reservation?>()
    val reservation: LiveData<Reservation?> by lazy {
        _reservation.asLiveData()
    }

    private val _shift = MutableLiveData<Shift>()
    val shift: LiveData<Shift> by lazy {
        _shift.asLiveData()
    }

    private val _reservationDetailsListItems = MutableLiveData<List<ReservationDetailsItem>>()
    val reservationDetailsListItems: LiveData<List<ReservationDetailsItem>> =
        _reservationDetailsListItems

    private val _guests = MutableLiveData<List<Guest>>()
    val guests: LiveData<List<Guest>> =
        _guests

    private val _guestCreated = SingleLiveEvent<Guest>()
    val guestCreated: LiveData<Guest> = _guestCreated

    private val _tempGuestCreated = SingleLiveEvent<Void>()
    val tempGuestCreated: LiveData<Void> = _tempGuestCreated

    private val _shouldProceedWithReservation = SingleLiveEvent<Void>()
    val shouldProceedWithReservation: LiveData<Void> = _shouldProceedWithReservation

    private val _voucherState =
        MutableStateFlow<ConsumableEvent<VoucherUpdateState<Reservation>>>(
            ConsumableEvent(VoucherUpdateState.Idle)
        )
    val voucherState: StateFlow<ConsumableEvent<VoucherUpdateState<Reservation>>> =
        _voucherState

    val reservationTakerMandatory = SingleLiveEvent<Boolean>()

    private val _loyaltyPointsUpdate = SingleLiveEvent<Boolean?>()
    val loyaltyPointsUpdate: LiveData<Boolean?> = _loyaltyPointsUpdate

    val deleteButtonVisible: Boolean
        get() {
            val hasPayments = reservation.value?.payments?.firstOrNull {
                arrayOf(
                    PaymentStatus.CAPTURED.toString(),
                    PaymentStatus.AUTHORIZED.toString(),
                    PaymentStatus.REFUNDED.toString(),
                    PaymentStatus.VOIDED.toString(),
                    PaymentStatus.PARTIALLY_REFUNDED.toString()
                ).contains(it.status)
            } != null

            return reservation.value?.online != true && createMode.value != true && !hasPayments
        }

    val conflictList = MediatorLiveData<List<ConflictItem>>()
    val isReservationUpdated: Boolean
        get() {
            val reservation = reservation.value
            val taggings = this.taggings?.takeIf { it.isNotEmpty() }
            return !(reservation?.guest?.id == initialReservationState?.guestId
                    && reservation?.channel == initialReservationState?.preference
                    && reservation?.status == initialReservationState?.status
                    && reservation?.walkIn == initialReservationState?.walkIn
                    && reservation?.startTime == initialReservationState?.time
                    && reservation?.startTime == initialReservationState?.date
                    && reservation?.duration == initialReservationState?.duration
                    && reservation?.covers == initialReservationState?.covers
                    && reservation?.tables == initialReservationState?.tables
                    && reservation?.notes == initialReservationState?.notes
                    && reservation?.comments == initialReservationState?.comments
                    && reservation?.createdBy == initialReservationState?.taker
                    && taggings == initialReservationState?.tags
                    && reservation?.attributes?.customFields == initialReservationState?.customFields)
        }

    init {
        conflictList.addSource(conflictsManager.conflictList) {
            conflictList.value = it
        }
    }

    fun updateInitialReservationState(reservation: Reservation?) {
        initialReservationState = ReservationState(
            reservation?.guest?.id,
            reservation?.tempName,
            reservation?.channel,
            reservation?.status,
            reservation?.walkIn,
            reservation?.startTime,
            reservation?.startTime,
            reservation?.duration,
            reservation?.covers,
            reservation?.waitQuote,
            reservation?.tables?.toList(),
            reservation?.notes,
            reservation?.comments?.toList(),
            reservation?.createdBy,
            reservation?.taggings,
            reservation?.attributes?.customFields?.toMap()
        )
    }

    fun reservation(reservation: Reservation?) {

        createMode.value = reservation?.id == ""

        taggings = when (taggings?.size?.compareTo(0)) {
            0 -> reservation?.taggings
            else -> taggings
        }

        initialStatus = reservation?.status.toString()

        _reservation.value = reservation

        createReservationDetailsListItems()

        _reservation.value?.waitQuote =
            if (createMode.value == true) eatManager.waitQuote() else reservation?.waitQuote ?: 0

        reservation?.id?.let {
            eatManager.insertOpenedReservation(it, Date())
        }

        shiftForTime()
        viewModelScope.launch(Dispatchers.IO) {
            closings()
            reservations()
        }
    }

    fun updateReservation() {

        if (validate()) return

        launch({

            update.postValue(true)

            val reservation =
                reservationsRepository.updateReservation(
                    restaurantId ?: eatManager.restaurantId(),
                    reservation.value?.id!!,
                    reservationBody(),
                    taggings
                )

            addOpenedAtTimestamp(reservation)

            _reservation.value = reservation

            update.postValue(false)

        }, false)
    }

    fun createReservation() {

        if (validate()) return

        launch({

            create.postValue(true)

            val reservation = reservationsRepository.createReservation(reservationBody(), taggings)

            addOpenedAtTimestamp(reservation)

            _reservation.value = reservation

            analyticsManager.trackFinishReservation()

            eatManager.addSuppressedNotificationId(reservation.id)

            create.postValue(false)

        }, false)
    }

    private fun addOpenedAtTimestamp(res: Reservation) {
        /*
         Adding 10 secs to be sure opened at is later then updated at
        */
        val openedDate = Date(Date().time.plus(10000))
        eatManager.insertOpenedReservation(res.id, openedDate)
        res.lastOpenedAt = openedDate
    }

    fun deleteReservation() {

        launch({

            delete.postValue(true)

            reservationsRepository.deleteReservation(reservation.value?.id!!)

            delete.postValue(false)

        }, false)
    }

    fun createGuest(guest: Guest, shouldCreateReservation: Boolean = false) {
        if (!validateGuest(guest)) {
            return
        }

        if (guest.phone.isNullOrEmpty() && guest.email.isNullOrEmpty()) {
            handleTempGuest(guest, shouldCreateReservation)
            return
        }

        loading(true)
        launch({
            val createdGuest = guestsRepository.createGuest(guest.toGuestBody(null, null))

            if (shouldCreateReservation) {
                reservation.value?.guest = createdGuest
                _shouldProceedWithReservation.call()
            } else {
                _guestCreated.value = createdGuest
                loading(false)
            }
        })
    }

    private fun handleTempGuest(guest: Guest, shouldCreateReservation: Boolean) {
        reservation.value?.tempName = String.format("%s %s", guest.firstName, guest.lastName)

        if (shouldCreateReservation) {
            _shouldProceedWithReservation.call()
        } else {
            _tempGuestCreated.call()
        }
    }

    private fun validateGuest(guest: Guest): Boolean {
        if (guest.firstName.isNullOrEmpty()) {
            setError(EatException("Validation", "First name can not be empty"))
            return false
        }
        return true
    }

    fun updateGuest(marketingAccepted: Boolean) {

        reservation.value?.guest?.let {

            it.marketingAccepted = marketingAccepted

            launch({

                val guest = guestsRepository.updateGuest(
                    reservation.value?.guest?.id!!,
                    it.toGuestBody(null, null),
                    reservation.value?.guest?.taggings
                )
                reservation.value?.guest = guest

            }, false)
        }
    }

    fun sendTableIsReadyMessage() {

        launch({

            val response = reservationsRepository.tableReady(
                reservation.value?.id!!
            )
            reservation.value?.waitlistTableReadyAt =
                response.reservation.attributes.waitlistTableReadyAt

            tableReadyMessageSent.value = true

        }, false)
    }

    fun guest(guest: Guest?) {

        _reservation.value?.guest = guest
        _reservation.value?.tempName = null

        reservation(reservation.value)
    }

    fun tableId(id: String?) {
        _reservation.value?.tables =
            tables.value?.first { it.id == id }?.let { listOf(it) }
    }

    fun restaurantId(restaurantId: String) {
        this.restaurantId = restaurantId
    }

    fun updateStatus(status: String) {

        if (!arrayOf(
                Status.Value.CONFIRMED.code,
                Status.Value.NOT_CONFIRMED.code
            ).contains(status)
        ) {
            sendMessage(false)
        }

        reservation.value?.status = status

        if (status == Status.Value.WAITLIST.code) {
            reservation.value?.waitlistQueuedAt = Date()
        }

        if (Status.isSeated(status)) {
            reservation.value?.startTime = ceilToQuarter(Date())
        }

        analyticsManager.trackReservationStatus()
    }

    fun updateComments(comments: MutableList<CommentModel>?) {
        _reservation.value?.comments = comments
    }

    fun updateTables(list: List<Table>?) {

        reservation.value?.tables =
            tables.value?.filter { table -> list?.any { it.id == table.id } == true }

        shiftForTime()
        checkConflicts()
    }

    fun updateDate(year: Int, month: Int, day: Int) {

        val c = Calendar.getInstance()

        c.time = when (reservation.value?.status == Status.Value.WAITLIST.code) {
            true -> reservation.value?.waitlistQueuedAt
            else -> reservation.value?.startTime
        } ?: Date()

        c.set(Calendar.YEAR, year)
        c.set(Calendar.MONTH, month)
        c.set(Calendar.DAY_OF_MONTH, day)

        when (reservation.value?.status == Status.Value.WAITLIST.code) {
            true -> {
                reservation.value?.waitlistQueuedAt = c.time
                reservation.value?.startTime = ceilToQuarter(c.time)
            }

            else -> {
                reservation.value?.startTime = c.time
                reservation.value?.waitlistQueuedAt = c.time
            }
        }

        shiftForTime()

        viewModelScope.launch(Dispatchers.IO) {
            closings()
            reservations()
        }
    }

    fun updateTime(stepper: StepperView.Step) {

        val c = Calendar.getInstance()

        val date = when (reservation.value?.status == Status.Value.WAITLIST.code) {
            true -> reservation.value?.waitlistQueuedAt
            else -> reservation.value?.startTime
        } ?: Date()

        c.time = date

        val increment = when (reservation.value?.status == Status.Value.WAITLIST.code) {
            true -> 1
            else -> 15
        }

        when (stepper) {
            StepperView.Step.PLUS -> c.add(Calendar.MINUTE, increment)
            StepperView.Step.MINUS -> c.add(Calendar.MINUTE, -increment)
        }

        updateTime(c.time)
    }

    fun updateTime(date: Date) {

        when (reservation.value?.status == Status.Value.WAITLIST.code) {
            true -> {
                reservation.value?.waitlistQueuedAt = date
                reservation.value?.startTime = ceilToQuarter(date)
            }

            else -> {
                reservation.value?.startTime = date
                reservation.value?.waitlistQueuedAt = date
            }
        }

        shiftForTime()
        checkConflicts()
    }

    fun timesList(): MutableList<SelectorItem> {

        val waitlist: Boolean = reservation.value?.status == Status.Value.WAITLIST.code
        val formatter = SimpleDateFormat("hh:mm a", Locale.US)

        val cMin = Calendar.getInstance()

        cMin.time = when (waitlist) {
            true -> reservation.value?.waitlistQueuedAt
            else -> reservation.value?.startTime
        } ?: Date()

        cMin.add(Calendar.HOUR, -4)
        cMin.set(Calendar.HOUR_OF_DAY, 0)
        cMin.set(Calendar.MINUTE, 0)

        val times = mutableListOf<SelectorItem>()

        for (i in 0..(if (waitlist) 287 else 95)) {

            val date = Calendar.getInstance()
            date.time = cMin.time
            date.add(Calendar.HOUR, 4)
            cMin.add(Calendar.MINUTE, if (waitlist) 5 else 15)

            val item = SelectorItem(
                "", formatter.format(date.time), date.time,
                isSelected = date.time == if (waitlist) reservation.value?.waitlistQueuedAt else reservation.value?.startTime,
                isHeader = false,
                isDisabled = false
            )
            times.add(item)
        }

        return times

    }

    fun updateCovers(stepper: StepperView.Step) {

        var covers: Int = reservation.value?.covers ?: 0

        when (stepper) {
            StepperView.Step.PLUS -> covers++
            StepperView.Step.MINUS -> covers--
        }

        if (covers < 1 || covers > MAX_COVERS) return

        updateCovers(covers)

    }

    fun updateCovers(covers: Int) {
        reservation.value?.covers = covers
        checkConflicts()
    }

    fun counterList(
        type: CounterListType,
        customFieldName: String? = null
    ): MutableList<SelectorItem> {

        val times = mutableListOf<SelectorItem>()

        for (i in 1..MAX_COVERS) {

            val isSelected = if (type == CounterListType.COVERS) {
                i == reservation.value?.covers
            } else {
                i == reservation.value?.attributes?.customFields?.get(customFieldName) as? Int
            }

            val item = SelectorItem(
                "", i.toString(), i,
                isSelected = isSelected,
                isHeader = false,
                isDisabled = false
            )
            times.add(item)
        }

        return times
    }

    fun updateDuration(stepper: StepperView.Step) {

        var duration: Int = reservation.value?.duration ?: 0

        when (stepper) {
            StepperView.Step.PLUS -> duration += 15 * 60
            StepperView.Step.MINUS -> duration -= 15 * 60
        }

        if (duration < 900 || duration > 86400) return

        updateDuration(duration)

    }

    fun updateDuration(duration: Int) {

        reservation.value?.duration = duration

    }

    fun durationsList(): MutableList<SelectorItem> {

        val durations = mutableListOf<SelectorItem>()

        for (i in 1..96) {

            val duration = i * 15 * 60

            val item = SelectorItem(
                "", durationFormatted(duration), duration,
                isSelected = duration == reservation.value?.duration,
                isHeader = false,
                isDisabled = false
            )
            durations.add(item)
        }

        return durations

    }

    fun updateWaitQuote(stepper: StepperView.Step) {

        var wait: Int = reservation.value?.waitQuote ?: 0

        when (stepper) {
            StepperView.Step.PLUS -> wait += 300
            StepperView.Step.MINUS -> wait -= 300
        }

        if (wait < 0 || wait > 100 * 60) return

        updateWaitQuote(wait)

    }

    fun updateWaitQuote(wait: Int) {

        reservation.value?.waitQuote = wait

    }

    // Change reservation status to CONFIRMED for NEW reservations when walkin is toggled ON and NOT_CONFIRMED when toggled off
    fun onWalkinCheckedChanged(isChecked: Boolean) {
        if (createMode.value == true) {

            if (!userChangedReservationStatus || (userChangedReservationStatus && reservation.value?.status == Status.Value.NOT_CONFIRMED.code)) {
                updateStatus(if (isChecked) Status.Value.CONFIRMED.code else Status.Value.NOT_CONFIRMED.code)
                userChangedReservationStatus = false
            }
        }
    }

    fun waitQuoteList(): MutableList<SelectorItem>? {

        val durations = mutableListOf<SelectorItem>()

        for (i in 0..20) {

            val waitQuote = i * 5 * 60

            val item = SelectorItem(
                "", String.format("%s min", waitQuote / 60), waitQuote,
                isSelected = waitQuote == reservation.value?.waitQuote,
                isHeader = false,
                isDisabled = false
            )
            durations.add(item)
        }

        return durations

    }

    fun updateNote(note: String?) {
        reservation.value?.notes = note
    }

    fun updateTaker(taker: String?) {
        reservation.value?.createdBy = taker
    }

    fun takersList(): MutableList<SelectorItem> {

        val takers = mutableListOf<SelectorItem>()

        val staff = <EMAIL> ?: emptyList()

        for (taker in staff) {

            val item = SelectorItem(
                taker.id,
                taker.name ?: "",
                taker,
                isSelected = taker.name == reservation.value?.createdBy,
                isHeader = false,
                isDisabled = false
            )
            takers.add(item)
        }

        return takers
    }

    fun reservationTaggingsList(): MutableList<SelectorItem>? {

        return eatManager.taggingSelectors(
            addItem = tagsList().size > 0,
            taggings = taggings,
            tags
        )
    }

    fun guestTaggingsList(): MutableList<SelectorItem>? {

        return eatManager.taggingSelectors(
            addItem = false,
            taggings = reservation.value?.guest?.taggings,
            tags
        )
    }

    fun tagsList(): MutableList<ItemsGroup> {

        return eatManager.tagSelectors(
            taggings = taggings,
            tagType = TagType.Reservation,
            tags = tags
        )
    }

    fun sourcesList(currentlySelected: String?): MutableList<SelectorItem>? {
        return BookingSource.sources?.filter { !it.logo.isNullOrEmpty() }?.map {
            SelectorItem(
                id = it.name,
                name = it.displayName,
                it,
                it.name == currentlySelected,
                isHeader = false,
                isDisabled = false,
                imageUrl = it.logo
            )
        }?.toMutableList()
    }

    fun updateSource(name: String?) {
        reservation.value?.attributes?.source = name
    }

    fun removeReservationTag(tag: SelectorItem) {

        val list = taggings?.toMutableList()

        list?.remove(
            taggings!!
                .find { it.name == tag.name })

        taggings = list
    }

    fun updateReservationTags(list: MutableList<ItemsGroup>?) {

        taggings = mutableListOf()

        list?.forEach { group ->
            taggings?.addAll(group.items.filter { it.isSelected }.map { item ->
                val tag = item.value as Tag
                Tagging(
                    TaggingAttributes(
                        tag.category?.attributes?.name,
                        tag.attributes.icon,
                        tag.attributes.name,
                        ""
                    ),
                    tag.id,
                    "",
                    tag.category?.attributes?.color
                )
            }.toMutableList())
        }
    }

    fun reservationVouchers(): List<Voucher> {
        val vouchers = <EMAIL>

        return vouchers?.filter { voucher ->
            val context = voucher.attributes.context ?: emptyList()
            context.contains(VoucherType.RESERVATION)
        } ?: emptyList()
    }

    fun updateVoucherAssignments(vouchers: List<Voucher>) {
        viewModelScope.launch {
            vouchersHandler.updateVoucherAssignmentsFlow<Reservation>(
                reservation.value!!.id,
                reservation.value?.attributes?.editedBy,
                vouchers
            ).collect {
                handleVoucherResponse(it)
                _voucherState.value = ConsumableEvent(it)
            }
        }
    }

    fun redeemVoucher(assignment: VoucherAssignmentModel) {
        viewModelScope.launch {
            vouchersHandler.redeemVoucherFlow<Reservation>(
                reservation.value!!.id,
                assignment = assignment,
                editedBy = reservation.value?.attributes?.editedBy
            ).collect {
                handleVoucherResponse(it)
                _voucherState.value = ConsumableEvent(it)
            }
        }
    }

    fun updateLoyaltyPoints(updatedPoints: Int) {
        reservation.value?.guest?.let {
            _loyaltyPointsUpdate.value = true
            val currentPoints = it.attributes?.loyaltyPoints ?: 0
            val isAddition = updatedPoints > currentPoints
            val diffPoints = abs(updatedPoints - currentPoints)

            val body = if (isAddition) {
                it.toGuestBody(null, null, addedPoints = diffPoints)
            } else {
                it.toGuestBody(null, null, removedPoints = diffPoints)
            }

            launch({
                val guest = guestsRepository.updateGuest(it.id, body, it.taggings)
                reservation.value?.guest = guest
                _loyaltyPointsUpdate.value = false
            })
        }
    }

    private fun handleVoucherResponse(voucherUpdateState: VoucherUpdateState<Reservation>) {
        if (voucherUpdateState is VoucherUpdateState.Error) {
            setError(voucherUpdateState.exception)
        } else if (voucherUpdateState is VoucherUpdateState.Success) {
            reservation.value?.voucherAssignments = voucherUpdateState.data.voucherAssignments
        }
    }

    fun shouldShowUpdate(): Boolean {
        return isNotLatestVersion() && featureFlagsManager.variationWhatsappChatFlow == "pay_up"
    }

    suspend fun closings() {
        val closings =
            closingsRepository.closings(reservation.value?.startTime ?: Date())
                .firstOrNull() ?: emptyList()
        <EMAIL> = closings.toMutableList()

        checkConflicts()
    }

    suspend fun reservations() {
        val reservations =
            reservationsRepository.reservations(
                reservation.value?.startTime ?: Date()
            ).firstOrNull() ?: emptyList()
        <EMAIL> = reservations.toMutableList()

        checkConflicts()
    }

    fun toggleGuestTagsExpanded() {
        val guestItem =
            reservationDetailsListItems.value?.first { it.itemType == ReservationDetailsItemType.GUEST.itemType }
        guestItem?.expanded = !guestItem!!.expanded
    }

    fun sendMessage(send: Boolean) {
        reservation.value?.sendMessage = send
    }

    fun updateCustomFields(field: Pair<String, Any>) {
        when (val fieldValue = field.second) {
            is Boolean -> {
                updateCustomFieldBool(fieldValue, field.first)
            }

            is String -> {
                updateCustomFieldText(fieldValue, field.first)
            }

            is Int -> {
                updateCustomFieldCount(fieldValue, field.first)
            }

            is ArrayList<*> -> {
                updateCustomFieldMulti(fieldValue as ArrayList<String>, field.first)
            }

            else -> {}
        }
    }

    fun updateCustomFields(customFieldName: String, stepper: StepperView.Step) {
        reservation.value?.let { reservation ->

            var count = reservation.attributes.customFields[customFieldName] as? Int ?: 0
            when (stepper) {
                StepperView.Step.PLUS -> count++
                StepperView.Step.MINUS -> count--
            }

            if (count < 0 || count > MAX_COVERS) return

            updateCustomFieldCount(count, customFieldName)
        }
    }

    fun checkConflicts() {
        launch({
            val shifts = shiftRepository.shifts.value

            conflictsManager.checkForConflicts(
                restaurantId ?: eatManager.restaurantId(),
                reservation.value?.tables ?: emptyList(),
                reservation.value!!,
                reservations,
                closings,
                shifts
            )
        })

    }

    fun updateEditor(id: String?) {
        reservation.value?.attributes?.editedBy = id
    }

    fun searchGuests(query: String) {
        if (query.isEmpty()) {
            _guests.value = emptyList()
            return
        }

        loading(true)

        launch({
            _guests.value = guestsRepository.guests(query, 1, 30)
            loading(false)
        })
    }

    fun country(phone: String?): Country? {
        val instance = PhoneNumberUtil.getInstance()
        return try {
            val parsedNumber = instance.parse(phone, null)
            val code = instance.getRegionCodeForNumber(parsedNumber)
            eatManager.countries().firstOrNull { it.code == code }
        } catch (e: Exception) {
            null
        }
    }

    fun vouchers(): List<Voucher> {
        return vouchers.value ?: emptyList()
    }

    fun vouchersEnabled(): Boolean {
        return eatManager.restaurant()?.vouchersEnabled == true && createMode.value == false
    }

    private fun updateCustomFieldBool(bool: Boolean, customFieldName: String) {
        if (bool) {
            reservation.value?.attributes?.customFields?.set(customFieldName, true)
        } else {
            reservation.value?.attributes?.customFields?.remove(customFieldName)
        }
        _reservationDetailsListItems.value?.first {
            it.customField?.attributes?.name == customFieldName
        }?.customField?.value = bool
    }

    private fun updateCustomFieldText(text: String, customFieldName: String) {
        if (text.isNotEmpty()) {
            reservation.value?.attributes?.customFields?.set(customFieldName, text)
        } else {
            reservation.value?.attributes?.customFields?.remove(customFieldName)
        }
        _reservationDetailsListItems.value?.first {
            it.customField?.attributes?.name == customFieldName
        }?.customField?.value = text
    }

    private fun updateCustomFieldCount(count: Int, customFieldName: String) {
        if (count == 0) {
            reservation.value?.attributes?.customFields?.remove(customFieldName)
        } else {
            reservation.value?.attributes?.customFields?.set(customFieldName, count)
        }
        _reservationDetailsListItems.value?.first {
            it.customField?.attributes?.name == customFieldName
        }?.customField?.value = count
    }

    private fun updateCustomFieldMulti(values: ArrayList<String>, customFieldName: String) {
        if (values.isEmpty()) {
            reservation.value?.attributes?.customFields?.remove(customFieldName)
        } else {
            reservation.value?.attributes?.customFields?.set(customFieldName, values)
        }
        _reservationDetailsListItems.value?.first {
            it.customField?.attributes?.name == customFieldName
        }?.customField?.value = values
    }

    fun saveReservationDetailsListSorting() {
        val list = reservationDetailsListItems.value
        val listString = Gson().toJson(list)
        sharedPreferences.edit {
            putString(RESERVATION_DETAILS_LIST_SORTING, listString)
        }
    }

    private fun loadReservationDetailsListSorting(): List<ReservationDetailsItem>? {
        val listString = sharedPreferences.getString(RESERVATION_DETAILS_LIST_SORTING, null)

        listString?.let {
            return try {
                val type = object : TypeToken<List<ReservationDetailsItem>>() {}.type
                Gson().fromJson<List<ReservationDetailsItem>>(listString, type)
            } catch (e: Exception) {

                return try {
                    val type = object : TypeToken<List<ReservationDetailsItemType>>() {}.type
                    Gson().fromJson<List<ReservationDetailsItemType>>(listString, type).map {
                        ReservationDetailsItem(it.itemType, false, null)
                    }
                } catch (e: Exception) {
                    return emptyList()
                }
            }
        }

        return emptyList()
    }

    private fun createReservationDetailsListItems() {
        val savedList = loadReservationDetailsListSorting()

        val defaultList = defaultSorting.map { it.toReservationListItem() }.toMutableList()

        val allCustomFields = customFieldsRepository.reservationCustomFields().toMutableList()

        reservation.value?.attributes?.customFields?.filterNot { customField ->
            customField.key in allCustomFields.map { it.attributes.name }
        }?.let { deletedFields ->
            allCustomFields.addAll(deletedFields.map {
                CustomField(
                    it.key,
                    CustomFieldAttributes(
                        CustomFieldComponent.RESERVATION,
                        it.key,
                        "",
                        it.key.labelized,
                        it.value.customFieldType,
                        it.value as? List<String>
                    ),
                    deleted = true,
                    it.value
                )
            })
        }

        defaultList.addAll(
            allCustomFields.map {
                ReservationDetailsItem(
                    it.attributes.name.hashCode(),
                    false,
                    it
                )
            }
        )

        val finalList = checkIfAnyNewItems(savedList, defaultList)
        addDeleteReservationItem(finalList)

        finalList.forEach {
            it.customField?.let { field ->
                field.value =
                    reservation.value?.attributes?.customFields?.get(field.attributes.name)
            }
        }

        _reservationDetailsListItems.value = finalList
    }

    private fun checkIfAnyNewItems(
        existingOrder: List<ReservationDetailsItem>?,
        defaultOrder: List<ReservationDetailsItem>
    ): MutableList<ReservationDetailsItem> {
        if (existingOrder == null) return defaultOrder.toMutableList()

        val existing = existingOrder.toMutableList()

        existing.removeAll {
            !(defaultOrder.contains(it)) && it.itemType != ReservationDetailsItemType.DIVIDER.itemType
        }

        for ((index, value) in defaultOrder.withIndex()) {
            if (!existing.contains(value)) {
                if (index >= existing.size) {
                    existing.add(value)
                } else {
                    existing.add(index, value)
                }
            }
        }

        return existing
    }

    private fun addDeleteReservationItem(items: MutableList<ReservationDetailsItem>) {
        if (!items.any { it.itemType == ReservationDetailsItemType.DELETE_RESERVATION.itemType }) {
            items.add(
                ReservationDetailsItem(
                    ReservationDetailsItemType.DELETE_RESERVATION.itemType,
                    false,
                    null
                )
            )
        }
    }

    private fun isNotLatestVersion(): Boolean {
        val appStoreVersion = featureFlagsManager.latestVersion
        val currentVersion = BuildConfig.VERSION_CODE

        return appStoreVersion > currentVersion
    }

    private fun reservationBody(): ReservationBody {

        if (reservation.value?.status == Status.Value.WAITLIST.code) reservation.value?.waitQuote?.let {
            eatManager.waitQuote(
                it
            )
        }

        return reservation.value!!.toReservationBody(
            eatManager.addedTags(
                taggings,
                reservation.value?.taggings
            ),
            eatManager.removedTags(taggings, reservation.value?.taggings)
        )
    }

    private fun shiftForTime() {
        launch({

            val shifts = shiftRepository.shifts.value

            _shift.postValue(
                shiftManager.shift(
                    reservation = reservation.value!!,
                    shifts = shifts
                )
            )
        })
    }

    private fun validate(): Boolean {

        return if (permissionReservationTaker.boolValue
            && reservation.value?.createdBy.isNullOrEmpty()
        ) {
            reservationTakerMandatory.value = true
            true

        } else false
    }
}