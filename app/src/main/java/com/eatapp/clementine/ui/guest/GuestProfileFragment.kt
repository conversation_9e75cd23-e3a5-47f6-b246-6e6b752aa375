package com.eatapp.clementine.ui.guest

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.ImageButton
import android.widget.LinearLayout
import androidx.core.view.children
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.eatapp.clementine.R
import com.eatapp.clementine.VoucherUpdateState
import com.eatapp.clementine.adapter.TagAdapter
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldType
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.databinding.GuestProfileFragmentBinding
import com.eatapp.clementine.databinding.ListItemCustomFieldMultiBinding
import com.eatapp.clementine.databinding.ToggleListItemBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_EMAIL
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_FIRST_NAME
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_LAST_NAME
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_NEW_FLOW
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_PHONE_NUMBER
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA_RESERVATION_ID
import com.eatapp.clementine.internal.Constants.GUEST_RESULT_NONE
import com.eatapp.clementine.internal.Constants.GUEST_SIMPLIFIED_MODE_EXTRA
import com.eatapp.clementine.internal.isTablet
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.internal.showToast
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.common.custom_fields.CustomFieldMultiAdapter
import com.eatapp.clementine.ui.home.HomeActivity
import com.eatapp.clementine.ui.home.more.guests.GuestsFragment
import com.eatapp.clementine.ui.reservation.ReservationActivity
import com.eatapp.clementine.ui.reservation.vouchers.VouchersAssignmentsAdapter
import com.eatapp.clementine.ui.reservation.vouchers.VouchersPopupDialog
import com.eatapp.clementine.ui.reservation.vouchers.VouchersSharedViewModel
import com.eatapp.clementine.views.ExpandableInputRegular
import com.eatapp.clementine.views.StepperView
import com.eatapp.clementine.views.StepperView.Companion.setDisabled
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date

/**
 * Interface for communication between GuestProfileFragment and its parent
 */
interface GuestProfileListener {
    fun onGuestDataChanged(guest: Guest?)
    fun onGuestValidationChanged(isValid: Boolean)
}


@AndroidEntryPoint
class GuestProfileFragment : BaseFragment<GuestProfileViewModel, GuestProfileFragmentBinding>() {

    private lateinit var adapter: TagAdapter
    private lateinit var vouchersAdapter: VouchersAssignmentsAdapter
    private val vouchersSharedViewModel by viewModels<VouchersSharedViewModel>()

    private var newFlow = false
    private var embeddedMode = false
    private var guestProfileListener: GuestProfileListener? = null

    enum class Dates {
        BIRTHDAY,
        ANNIVERSARY
    }

    companion object {
        private const val EMBEDDED_MODE_EXTRA = "embedded_mode"

        fun newInstance(
            guest: Guest?,
            simplifiedMode: Boolean,
            firstName: String?,
            lastName: String?,
            phone: String?,
            email: String?,
            reservationId: String?,
            newFlow: Boolean = false,
            embeddedMode: Boolean = false
        ) = GuestProfileFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(GUEST_EXTRA, guest)
                    putBoolean(GUEST_SIMPLIFIED_MODE_EXTRA, simplifiedMode)
                    putString(GUEST_EXTRA_PHONE_NUMBER, phone)
                    putString(GUEST_EXTRA_FIRST_NAME, firstName)
                    putString(GUEST_EXTRA_LAST_NAME, lastName)
                    putString(GUEST_EXTRA_EMAIL, email)
                    putString(GUEST_EXTRA_RESERVATION_ID, reservationId)
                    putBoolean(GUEST_EXTRA_NEW_FLOW, newFlow)
                    putBoolean(EMBEDDED_MODE_EXTRA, embeddedMode)
                }
            }
    }

    override fun viewModelClass() = GuestProfileViewModel::class.java

    override fun inflateLayout() = GuestProfileFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        // Check if this fragment is in embedded mode
        embeddedMode = arguments?.getBoolean(EMBEDDED_MODE_EXTRA, false) ?: false

        arguments?.getParcelable<Guest>(GUEST_EXTRA)?.let { guest ->
            vm.setGuest(guest)
            if (!embeddedMode) {
                activity?.findViewById<ImageButton>(R.id.createReservation)?.visibleOrGone = true
            }
        } ?: run {
            vm.setCreateMode()
            if (!embeddedMode) {
                activity?.findViewById<ImageButton>(R.id.createReservation)?.visibleOrGone = false
            }
        }

        arguments?.getString(GUEST_EXTRA_PHONE_NUMBER)?.let { vm.phoneNumber(it) }

        arguments?.getString(GUEST_EXTRA_FIRST_NAME)?.let { vm.firstName(it) }

        arguments?.getString(GUEST_EXTRA_LAST_NAME)?.let { vm.lastName(it) }

        arguments?.getString(GUEST_EXTRA_EMAIL)?.let { vm.email(it) }

        arguments?.getBoolean(GUEST_SIMPLIFIED_MODE_EXTRA).let {
            if (it!!) {
                vm.simplifiedMode.value = true
            }
        }

        arguments?.getBoolean(GUEST_EXTRA_NEW_FLOW)?.let {
            this.newFlow = it
        }

        vm.updateReservationId(arguments?.getString(GUEST_EXTRA_RESERVATION_ID))

        bindUI()
        observe()
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun bindUI() = with(binding) {

        guestDataLayout.birthday.cont.setOnClickListener(DateClickListener(Dates.BIRTHDAY))
        guestDataLayout.anniversary.cont.setOnClickListener(DateClickListener(Dates.ANNIVERSARY))

        if (!embeddedMode) {
            activity?.findViewById<ImageButton>(R.id.createReservation)?.setOnClickListener { createReservation() }
        }

        if (activity?.javaClass?.simpleName == HomeActivity::class.simpleName) {
            scrollView.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
                hideKeyboard()
            })
        }

        guestDataLayout.marketingOptIn.cbToggle.setOnCheckedChangeListener { _, isChecked ->
            vm.guest.value?.marketingAccepted = isChecked
            notifyGuestDataChanged()
        }

        if (newFlow) {
            binding.createBtn.setTitle(getString(R.string.update_user))
        }

        // Hide bottom buttons in embedded mode
        if (embeddedMode) {
            bottomButtons.visibility = View.GONE
            // Disable nested scrolling to prevent conflicts with parent scroll view
            scrollView.isNestedScrollingEnabled = false

            // Adjust scroll view constraints for embedded mode
            val layoutParams = scrollView.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            layoutParams.height = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT
            layoutParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            layoutParams.bottomToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            scrollView.layoutParams = layoutParams
        }

        updateGuestNotes()
    }

    private fun setupVouchers() {
        binding.guestDataLayout.containerVouchers.visibleOrGone = vm.vouchers().isNotEmpty() && vm.vouchersEnabled() && vm.createMode.value != true

        vouchersAdapter = VouchersAssignmentsAdapter {
            if (vm.isFreemium || vm.permissionGuest.value?.boolValue == false) {
                return@VouchersAssignmentsAdapter
            }
            if (it.redeemed) {
                requireContext().showToast(getString(R.string.voucher_redeemed_label))
            } else {
                vouchersSharedViewModel.updateVouchers(listOf(it))
                val dialog = VouchersPopupDialog.newInstance(VouchersPopupDialog.DialogType.REDEEM)
                dialog.show(childFragmentManager, null)
            }
        }

        binding.guestDataLayout.rvVoucherAssignments.adapter = vouchersAdapter
        vouchersAdapter.submitList(vm.guest.value?.voucherAssignments)

        binding.guestDataLayout.ivAddVoucher.setOnClickListener {
            if (vm.guestVouchers().isEmpty()) {
                requireContext().showToast(getString(R.string.assigned_vouchers_label_guest))
            } else {
                vouchersSharedViewModel.updateVouchers(vm.guestVouchers())
                val dialog = VouchersPopupDialog.newInstance(VouchersPopupDialog.DialogType.ASSIGN)
                dialog.show(childFragmentManager, null)
            }
        }
    }

    val listener: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun afterTextChanged(s: Editable?) {}
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            vm.updateNote(s.toString())
            notifyGuestDataChanged()
        }
    }

    private fun updateGuestNotes() {
        binding.guestDataLayout.guestNotes.updateView(
            requireContext().getString(R.string.guest_notes),
            requireContext().getString(R.string.guest_notes_hint),
            vm.guest.value?.notes,
            vm.isFreemium,
            listener
        )
    }

    @SuppressLint("SetTextI18n")
    private fun setupCustomFields() {
        val customFields = vm.customFields()
        val guestDataLayout = binding.guestDataLayout.root as LinearLayout

        guestDataLayout.children
            .filter { it.tag == 999 }
            .toList()
            .forEach { guestDataLayout.removeView(it) }

        customFields.forEach { field ->
            when (field.type) {
                CustomFieldType.TEXT -> {
                    val view = ExpandableInputRegular(requireContext())

                    val listener: TextWatcher = object : TextWatcher {
                        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                        override fun afterTextChanged(s: Editable?) {}
                        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                            vm.updateCustomFields(Pair(field.attributes.name, s.toString()))
                            notifyGuestDataChanged()
                        }
                    }
                    view.binding.enabled = !vm.isFreemium && vm.permissionGuest.value?.boolValue == true
                    view.updateView(field.label + if (field.deleted) binding.root.context.getString(R.string.deleted_custom_field_label) else "",
                        getString(R.string.custom_field_text_input_hint), field.value?.toString() ?: "", vm.isFreemium || vm.permissionGuest.value?.boolValue == false, listener)
                    view.tag = 999
                    guestDataLayout.addView(view)
                }
                CustomFieldType.BOOLEAN -> {
                    val view = ToggleListItemBinding.inflate(layoutInflater)
                    view.enabled = !vm.isFreemium && vm.permissionGuest.value?.boolValue == true
                    view.key = field.label + if (field.deleted) binding.root.context.getString(R.string.deleted_custom_field_label) else ""
                    view.value = (field.value as? Boolean) == true
                    view.cbToggle.setOnCheckedChangeListener { _, isChecked ->
                        vm.updateCustomFields(Pair(field.attributes.name, isChecked))
                        notifyGuestDataChanged()
                    }
                    view.root.tag = 999
                    guestDataLayout.addView(view.root)
                }
                CustomFieldType.COUNTABLE -> {
                    val view = StepperView(requireContext())
                    view.setDisabled(vm.isFreemium || vm.permissionGuest.value?.boolValue == false)
                    view.listener = {
                        vm.updateCustomFields(field.attributes.name, it)
                        val updatedField = vm.customFields().first { it.attributes.name == field.attributes.name }
                        view.setValue((updatedField.value as? Int)?.toString() ?: "0")
                        notifyGuestDataChanged()
                    }
                    view.setOnClickListener {
                        showCustomFieldCountList(field.attributes.name, view)
                    }

                    view.setTitle(field.label)
                    view.setValue((field.value as? Int)?.toString() ?: "0")
                    view.titleAllCaps(false)
                    view.tag = 999
                    guestDataLayout.addView(view)
                }

                CustomFieldType.MULTI -> {
                    val view = ListItemCustomFieldMultiBinding.inflate(layoutInflater)
                    view.tvTitle.text = field.label + if (field.deleted) binding.root.context.getString(R.string.deleted_custom_field_label) else ""
                    val adapter = CustomFieldMultiAdapter().apply {
                        selectedOptions = field.value as? MutableList<String> ?: mutableListOf()
                        enabled = !vm.isFreemium && vm.permissionGuest.value?.boolValue == true
                        submitList(field.attributes.options)
                        selectedListener = {
                            field.attributes.name.let { name ->
                                vm.updateCustomFields(Pair(name, it))
                                notifyGuestDataChanged()
                            }
                        }
                    }
                    view.rvChoices.adapter = adapter
                    view.root.tag = 999
                    guestDataLayout.addView(view.root)
                }

                // Don't display unsupported fields
                null -> {}
            }
        }
    }

    private fun observe() {

        vm.vouchers.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                setupVouchers()
            }
        }

        vm.customFields.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                setupCustomFields()
            }
        }

        vm.tags.observe(viewLifecycleOwner) {
            adapter = TagAdapter(
                vm.permissionGuest.value?.boolValue == true && !vm.isFreemium,
                addTagListener = {
                    val list = vm.tagsList()
                    bottomSheetGroupDialog(list, resources.getString(R.string.add_tags), false) {
                        vm.updateTags(list)
                        adapter.submitListToAdapter(vm.taggingsList())
                    }
                }, removeTagListener = {
                    vm.removeTag(it)
                    adapter.submitListToAdapter(vm.taggingsList())
                })

            binding.guestDataLayout.tagsList.adapter = adapter
            adapter.submitListToAdapter(vm.taggingsList())
        }

        vm.deleteCompleted.observe(viewLifecycleOwner) {
            if (it) {
                binding.deleteBtn.hideLoading()
                navigateBack(Constants.GUEST_RESULT_DELETED)
            }
        }

        vm.deleteInProgress.observe(viewLifecycleOwner) {
            if (it) {
                binding.deleteBtn.showLoading()
                hideKeyboard()
            } else {
                binding.deleteBtn.hideLoading()
            }
        }

        vm.update.observe(viewLifecycleOwner) {

            when (it) {
                true -> {
                    binding.updateBtn.showLoading()
                    hideKeyboard()
                }
                else -> {
                    binding.updateBtn.hideLoading()
                    navigateBack(Constants.GUEST_RESULT_UPDATED)
                }
            }
        }

        vm.create.observe(viewLifecycleOwner) {

            when (it) {
                true -> {
                    binding.createBtn.showLoading()
                    hideKeyboard()
                }
                else -> {
                    binding.createBtn.hideLoading()
                    navigateBack(Constants.GUEST_RESULT_ADDED)
                }
            }
        }

        vm.call.observe(viewLifecycleOwner) {
            val intent = Intent(Intent.ACTION_DIAL, Uri.fromParts("tel", it, null))
            startActivity(intent)
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                vm.voucherState.collect { state ->
                    when (state) {
                        is VoucherUpdateState.Loading -> {
                            vouchersSharedViewModel.showVoucherLoading(true)
                        }

                        is VoucherUpdateState.Success -> {
                            vouchersSharedViewModel.showVoucherLoading(false)
                            vouchersSharedViewModel.dismissPopup()
                            vouchersAdapter.submitList(vm.guest.value?.voucherAssignments)
                        }

                        is VoucherUpdateState.Error -> {
                            vouchersSharedViewModel.showVoucherLoading(false)
                        }

                        is VoucherUpdateState.Idle -> { /* Do nothing */ }
                    }
                }
            }
        }

        vouchersSharedViewModel.assignVouchers.observe(viewLifecycleOwner) { vouchers ->
            if (vouchers.isEmpty()) {
                requireContext().showToast(getString(R.string.empty_assign_list_label))
            } else {
                vm.updateVoucherAssignments(vouchers)
            }
        }

        vouchersSharedViewModel.redeemVoucher.observe(viewLifecycleOwner) { voucherAssignment ->
            vm.redeemVoucher(voucherAssignment)
        }
    }

    fun updateGuest(guest: Guest) {
        vm.createMode.value = false
        vm.setGuest(guest)
        adapter.submitListToAdapter(vm.taggingsList())
        vouchersAdapter.submitList(guest.voucherAssignments)
        setupCustomFields()
        updateGuestNotes()
        binding.root.clearFocus()
    }

    private inner class DateClickListener(
        private var date: Dates
    ) : View.OnClickListener {

        override fun onClick(view: View) {

            val c = Calendar.getInstance()

            when (date) {
                Dates.BIRTHDAY -> c.time = vm.guest.value?.birthday ?: Date()
                Dates.ANNIVERSARY -> c.time = vm.guest.value?.anniversary ?: Date()
            }

            val datePicker = DatePickerDialog(
                requireContext(), { _, y, m, d ->

                    vm.updateDate(date, y, m, d)
                    notifyGuestDataChanged()

                }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)
            )

            datePicker.show()
        }
    }

    fun guest(): Pair<Int, Guest?> {

        try {
            return when (vm.guestStatus) {
                GuestProfileViewModel.GuestStatus.Updated -> Pair(
                    Constants.GUEST_RESULT_UPDATED,
                    vm.guest.value
                )
                else -> Pair(GUEST_RESULT_NONE, null)
            }
        } catch (_: UninitializedPropertyAccessException) {}

        return Pair(GUEST_RESULT_NONE, null)
    }

    private fun navigateBack(result: Int) {

        if (context?.isTablet == false || vm.createMode.value == true) {
            val intent = Intent()
            intent.putExtra(GUEST_EXTRA, vm.guest.value)
            activity?.setResult(result, intent)
            activity?.finish()
        } else {
            val guestsFragment = requireActivity().supportFragmentManager.fragments[0]
                .childFragmentManager.fragments[0] as? GuestsFragment
            val intent = Intent()
            intent.putExtra(GUEST_EXTRA, vm.guest.value)
            guestsFragment?.onActivityResult(Constants.GUEST_REQUEST, result, intent)
        }
    }

    private fun createReservation() {

        if (!vm.permissionReservation.boolValue) {
            requireContext().showErrorAlert(
                resources.getString(R.string.create_reservation),
                vm.permissionReservation.errorMessage
            )
            return
        }

        vm.analyticsManager.trackAddReservationFromGuest()

        val intent = Intent(context, ReservationActivity::class.java)
        intent.putExtra(GUEST_EXTRA, vm.guest.value)
        startActivityForResult(intent, Constants.RESERVATION_REQUEST)
    }

    private fun showCustomFieldCountList(name: String, view: StepperView) {

        val list = vm.counterList(customFieldName = name)
        bottomSheetDialog(list, "", true, centerItemHorizontally = true) {
            vm.updateCustomFields(Pair(name, list?.first { it.isSelected }?.value as Int))
            val updatedField = vm.customFields().first { it.attributes.name == name }
            view.setValue((updatedField.value as? Int)?.toString() ?: "0")
            notifyGuestDataChanged()
        }
    }

    /**
     * Set the listener for guest profile changes (used in embedded mode)
     */
    fun setGuestProfileListener(listener: GuestProfileListener?) {
        this.guestProfileListener = listener
    }

    /**
     * Get the current guest data (used in embedded mode)
     */
    fun getCurrentGuest(): Guest? {
        return vm.guest.value
    }

    /**
     * Validate the current guest data (used in embedded mode)
     */
    fun validateGuestData(): Boolean {
        return !vm.guest.value?.firstName.isNullOrBlank()
    }

    /**
     * Save the guest data (used in embedded mode)
     */
    fun saveGuestData(): Boolean {
        return if (validateGuestData()) {
            if (vm.createMode.value == true) {
                vm.onCreateGuest()
            } else {
                vm.onUpdateGuest()
            }
            true
        } else {
            false
        }
    }

    /**
     * Notify parent about guest data changes
     */
    private fun notifyGuestDataChanged() {
        if (embeddedMode) {
            guestProfileListener?.onGuestDataChanged(vm.guest.value)
            guestProfileListener?.onGuestValidationChanged(validateGuestData())
        }
    }
}
