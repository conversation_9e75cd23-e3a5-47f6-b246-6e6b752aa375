package com.eatapp.clementine.data.network.response.payment


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.Date

@Parcelize
data class PaymentAttributes(
    @SerializedName("amount")
    val amount: Double,
    @SerializedName("currency")
    val currency: String,
    @SerializedName("description")
    val description: String?,
    @SerializedName("gateway")
    val gateway: String,
    @SerializedName("guest_email")
    val guestEmail: String?,
    @SerializedName("guest_first_name")
    val guestFirstName: String?,
    @SerializedName("guest_last_name")
    val guestLastName: String?,
    @SerializedName("guest_phone")
    val guestPhone: String?,
    @SerializedName("payment_widget_url")
    val paymentWidgetUrl: String?,
    @SerializedName("reservation_id")
    val reservationId: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("notes")
    val notes: String?,
    @SerializedName("refund_balance")
    val refundBalance: Double,
    @SerializedName("refund_amount")
    val refundAmount: Double,
    @SerializedName("created_at")
    val createdAt: Date?,
    @SerializedName("updated_at")
    val updatedAt: Date?,
    @SerializedName("expires_at")
    val expiresAt: String?,
    @SerializedName("auto_cancel_at")
    val autoCancelAt: Date?,
    @SerializedName("gateway_actions")
    var gatewayActions: List<GatewayAction>?,
    @SerializedName("capture_url")
    val captureUrl: String?,
    @SerializedName("refund_url")
    val refundUrl: String?,
    @SerializedName("cancel_url")
    val cancelUrl: String?,
    @SerializedName("void_url")
    val voidUrl: String?,
    @SerializedName("may_send_reminder")
    var maySendReminder: Boolean,
    @SerializedName("may_update")
    val mayUpdate: Boolean,
    @SerializedName("payment_rule_id")
    val ruleId: String?,
    @SerializedName("charge_strategy")
    val chargeStrategy: String,
) : Parcelable